import { useQuery } from "@tanstack/react-query";
import type { DiscordApiResponse } from "shared";

export const fetchDiscord = async () => {
	const { data, error } = useQuery({
		queryKey: ["discord-status"],
		queryFn: async () => {
			const response = await fetch("/api/discord", {
				headers: { Accept: "application/json" },
			});
			if (!response.ok) {
				throw new Error("Failed to fetch Discord status");
			}
			return (await response.json()) as DiscordApiResponse;
		},
		refetchInterval: 30000, // Refetch every 30 seconds
	});

	if (error || !data?.success || !data?.data) {
		return {
			data: undefined,
			error,
		};
	}

	return { data, success: data?.success === true };
};
